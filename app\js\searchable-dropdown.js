/**
 * Searchable Dropdown Component
 *
 * Usage:
 * 1. Include this script in your page
 * 2. Call initSearchableDropdowns() to initialize all dropdowns with the 'searchable-select' class
 * 3. Or call makeDropdownSearchable(selectElement) to make a specific dropdown searchable
 */

(function() {
    'use strict';

    /**
     * Initialize all dropdowns with the 'searchable-select' class
     */
    function initSearchableDropdowns() {
        document.querySelectorAll('select.searchable-select').forEach(select => {
            makeDropdownSearchable(select);
        });
    }

    /**
     * Make a specific dropdown searchable
     * @param {HTMLSelectElement} selectElement - The select element to enhance
     */
    function makeDropdownSearchable(selectElement) {
        if (!selectElement ||
            selectElement.classList.contains('searchable-initialized') ||
            selectElement.id === 'nb-results-page') {
            return;
        }

        const isSimpleMode = selectElement.dataset.simpleMode === 'true';
        const noBracketFilter = selectElement.dataset.noBracketFilter === 'true';

        selectElement.style.visibility = 'hidden';
        selectElement.style.position = 'absolute';
        selectElement.style.display = 'none';

        if (selectElement.hasAttribute('placeholder')) {
            selectElement.placeholder = '';
        }

        selectElement.classList.add('searchable-initialized');

        const originalOptions = Array.from(selectElement.options);

        let initialPlaceholderText = '';
        if (originalOptions.length > 0 && (originalOptions[0].value === 'search' || originalOptions[0].dataset.type === 'search')) {
            initialPlaceholderText = originalOptions[0].textContent.trim();
        } else if (selectElement.getAttribute('placeholder') && selectElement.getAttribute('placeholder').trim() !== '') {
            initialPlaceholderText = selectElement.getAttribute('placeholder').trim();
        }

        const customSelect = document.createElement('div');
        customSelect.className = 'custom-select-container';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'custom-select-input form-control';
        searchInput.setAttribute('autocomplete', 'off');
        searchInput.placeholder = initialPlaceholderText;

        if (selectElement.id) {
            searchInput.id = selectElement.id + '-search-input';
        } else {
            searchInput.id = 'custom-search-input-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
        }

        if (selectElement.name) {
            searchInput.name = selectElement.name + '-search-input';
        }

        let currentFolderInfo = null;
        let lastSelectedFolderInfo = null;
        let prefixManuallyDeleted = false;
        let hasPerformedSearchInCurrentFolder = false;
        let isPerformingExternalSearchAction = false;
        let debounceTimeout;

        // Helper function for robust text extraction and normalization
        function extractAndNormalizeSearchTerm(text) {
            if (!text || typeof text !== 'string') return '';

            let normalized = text.trim();

            normalized = normalized.replace(/^\s*[-•*]\s*/, '');
            normalized = normalized.replace(/\s+\(\d+\)$/, '');
            normalized = normalized.replace(/\s+\[\d+\]$/, '');
            normalized = normalized.replace(/\s*:\s*$/, '');

            normalized = normalized.replace(/\s+/g, ' ').trim();

            normalized = normalized.replace(/[^\w\s\-_.]/g, ' ').replace(/\s+/g, ' ').trim();

            return normalized;
        }

        function getSelectedFolderInfo() {
            if (isSimpleMode) return null;
            // Check both normal and virtual folder trees
            const normalTree = document.querySelector('#normal-folders-tree');
            const virtualTree = document.querySelector('#virtual-folders-tree');

            let selectedFolderElement = null;

            // First check the normal folders tree
            if (normalTree) {
                selectedFolderElement = normalTree.querySelector('li.folder-selected');
            }

            // If not found in normal tree, check virtual folders tree
            if (!selectedFolderElement && virtualTree) {
                selectedFolderElement = virtualTree.querySelector('li.folder-selected');
            }

            // Fallback: check any folders tree with the generic selector
            if (!selectedFolderElement) {
                const foldersTree = document.querySelector('.folders-tree');
                if (foldersTree) {
                    selectedFolderElement = foldersTree.querySelector('li.folder-selected');
                }
            }

            if (selectedFolderElement) {
                const folderId = selectedFolderElement.getAttribute('folderId');
                const folderNameElement = selectedFolderElement.querySelector('.folder-name');
                if (folderId && folderNameElement) {
                    const folderName = (folderNameElement.getAttribute('data-full-name') || folderNameElement.textContent || '').trim();
                    if (folderName) {
                        // Remove icon and numbers from folder name
                        const cleanFolderName = folderName.replace(/^\s*<i[^>]*>.*?<\/i>\s*/, '').replace(/\s*\[[^\]]*\]\s*$/, '').replace(/\s*\([^)]*\)\s*$/, '').trim();
                        return {
                            id: folderId,
                            name: cleanFolderName,
                            prefix: cleanFolderName + ": "
                        };
                    }
                }
            }
            return null;
        }

        function initiateMainSearch() {
            isPerformingExternalSearchAction = true;

            const searchActionOption = Array.from(selectElement.options).find(opt => opt.dataset.type === 'search' || opt.value === 'search');
            if (!searchActionOption) {
                isPerformingExternalSearchAction = false;
                return;
            }

            let query = searchInput.value;
            const originalInputValue = searchInput.value;

            if (currentFolderInfo && currentFolderInfo.prefix && query.startsWith(currentFolderInfo.prefix)) {
                query = query.substring(currentFolderInfo.prefix.length);
            }

            const trimmedQuery = query.trim();
            if (trimmedQuery === '') {
                isPerformingExternalSearchAction = false;
                return;
            }

            searchInput.value = trimmedQuery;

            selectElement.value = searchActionOption.value;
            const event = new Event('change', { bubbles: true });
            selectElement.dispatchEvent(event);

            setTimeout(() => {
                searchInput.value = originalInputValue;
                isPerformingExternalSearchAction = false;
            }, 100);

            hasPerformedSearchInCurrentFolder = true;
            dropdownList.style.display = 'none';
        }

        function updateSearchPlaceholderAndPrefix(selectedTextContentForDisplay = null) {
            if (isSimpleMode) return;
            if (isPerformingExternalSearchAction) {
                currentFolderInfo = getSelectedFolderInfo();
                if (currentFolderInfo) lastSelectedFolderInfo = currentFolderInfo;
                return;
            }

            let newFolderInfo = getSelectedFolderInfo();
            if (!newFolderInfo && lastSelectedFolderInfo) {
                newFolderInfo = lastSelectedFolderInfo;
            }
            if (newFolderInfo) lastSelectedFolderInfo = newFolderInfo;

            const isOptionExplicitlySelected = selectedTextContentForDisplay !== null && selectElement.hasAttribute('data-user-selected');

            let placeholderTextToShow = initialPlaceholderText;
            if (newFolderInfo && newFolderInfo.prefix && !prefixManuallyDeleted) {
                placeholderTextToShow = newFolderInfo.prefix;
            }
            searchInput.placeholder = placeholderTextToShow;

            if (isOptionExplicitlySelected) {
                if (newFolderInfo && newFolderInfo.prefix && !prefixManuallyDeleted) {
                    searchInput.value = newFolderInfo.prefix;
                } else {
                    searchInput.value = selectedTextContentForDisplay;
                }
            } else if (!prefixManuallyDeleted) {
                let userText = searchInput.value;
                const currentPrefixInInput = newFolderInfo && searchInput.value.startsWith(newFolderInfo.prefix) ? newFolderInfo.prefix : (currentFolderInfo && searchInput.value.startsWith(currentFolderInfo.prefix) ? currentFolderInfo.prefix : '');

                if (currentPrefixInInput) {
                     userText = searchInput.value.substring(currentPrefixInInput.length);
                } else {
                    const oldPrefixMatch = searchInput.value.match(/^(.*?:\s)(.*)$/);
                    if (oldPrefixMatch && oldPrefixMatch[2] !== undefined && (!newFolderInfo || newFolderInfo.prefix !== oldPrefixMatch[1])) {
                        userText = oldPrefixMatch[2];
                    }
                }

                if (newFolderInfo && newFolderInfo.prefix) {
                    if (searchInput.value !== newFolderInfo.prefix + userText) {
                         searchInput.value = newFolderInfo.prefix + userText;
                    }
                } else {
                    // No folder selected - clear any existing prefix
                    if (currentFolderInfo && currentFolderInfo.prefix && searchInput.value.startsWith(currentFolderInfo.prefix)) {
                        // Remove the current prefix and keep only the user text
                        const textAfterPrefix = searchInput.value.substring(currentFolderInfo.prefix.length);
                        searchInput.value = textAfterPrefix;
                    } else if (searchInput.value.includes(': ')) {
                        // Remove any other prefix pattern
                        const prefixMatch = searchInput.value.match(/^(.*?:\s)(.*)$/);
                        if (prefixMatch && prefixMatch[2] !== undefined) {
                            searchInput.value = prefixMatch[2];
                        }
                    }
                }
            }

            currentFolderInfo = newFolderInfo;
        }

        if (!isSimpleMode) {
            updateSearchPlaceholderAndPrefix();
        }

        searchInput.style.outline = 'none';
        searchInput.addEventListener('focus', function() {
            this.style.boxShadow = 'none';
            if (!isSimpleMode) {
                const wasManuallyDeletedPriorToFocus = prefixManuallyDeleted;
                updateSearchPlaceholderAndPrefix();

                if (!wasManuallyDeletedPriorToFocus) {
                    if (currentFolderInfo && currentFolderInfo.prefix && !this.value.startsWith(currentFolderInfo.prefix)) {
                        const currentValText = this.value;
                        this.value = currentFolderInfo.prefix + currentValText;
                    } else if ((!currentFolderInfo || !currentFolderInfo.prefix) && this.value.includes(': ')) {
                        const valOldPrefixIndex = this.value.lastIndexOf(": ");
                        if (valOldPrefixIndex !== -1) {
                            this.value = this.value.substring(valOldPrefixIndex + 2);
                        }
                    }
                }
            }
            this.placeholder = '';
            positionDropdown();
        });

        const dropdownList = document.createElement('div');
        dropdownList.className = 'custom-select-dropdown';
        dropdownList.style.display = 'none';

        customSelect.appendChild(searchInput);

        document.body.appendChild(dropdownList);
        selectElement.parentNode.insertBefore(customSelect, selectElement);

        selectElement.style.display = 'none';

        searchInput.value = '';

        function populateDropdown(filterTerm = '') {
            dropdownList.innerHTML = '';
            let actualFilterTerm = filterTerm;
            if (currentFolderInfo && currentFolderInfo.prefix &&
                filterTerm.toLowerCase().startsWith(currentFolderInfo.prefix.toLowerCase())) {
                actualFilterTerm = filterTerm.substring(currentFolderInfo.prefix.length);
            }

            const optionsToDisplay = originalOptions.filter(option => {
                const isSearchAction = option.dataset.type === 'search' || option.value === 'search';
                if (isSearchAction) {
                    return false;
                }

                if (noBracketFilter) {
                    return true;
                }

                const text = option.textContent;
                const hasOpenBracket = text.includes('[');
                const hasCloseBracket = text.includes(']');
                const hasBrackets = hasOpenBracket && hasCloseBracket;

                return hasBrackets;
            });

            optionsToDisplay.sort((a, b) => {
                return a.textContent.localeCompare(b.textContent);
            });

            const uniqueOptionsTextContents = new Set();
            const trulyUniqueOptionsToDisplay = optionsToDisplay.filter(option => {
                if (!uniqueOptionsTextContents.has(option.textContent)) {
                    uniqueOptionsTextContents.add(option.textContent);
                    return true;
                }
                return false;
            });

            const filteredDisplayOptions = actualFilterTerm ?
                trulyUniqueOptionsToDisplay.filter(option =>
                    option.textContent.toLowerCase().includes(actualFilterTerm.toLowerCase())
                ) :
                trulyUniqueOptionsToDisplay;

            if (filteredDisplayOptions.length === 0 && actualFilterTerm.trim() !== '') {
                const noResults = document.createElement('div');
                noResults.className = 'custom-select-option no-results';
                const noResultsText = document.getElementById('no-results-text')?.value || 'No folders found';
                noResults.textContent = noResultsText;
                dropdownList.appendChild(noResults);
                return;
            }

            let appendedCount = 0;
            filteredDisplayOptions.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'custom-select-option';
                optionElement.textContent = option.textContent;
                optionElement.dataset.value = option.value;

                if (option.value === selectElement.value) {
                    optionElement.classList.add('selected');
                }

                optionElement.addEventListener('click', function() {
                    const clickedOptionOriginalValue = this.dataset.value;
                    const originalOptElement = originalOptions.find(opt => opt.value === clickedOptionOriginalValue);

                    if (!originalOptElement) {
                        dropdownList.style.display = 'none';
                        return;
                    }

                    if (isSimpleMode) {
                        searchInput.value = originalOptElement.textContent.trim();
                        selectElement.value = originalOptElement.value;
                        dropdownList.style.display = 'none';

                        const event = new Event('change', { bubbles: true });
                        selectElement.dispatchEvent(event);
                        return;
                    }

                    const folderId = originalOptElement.value;
                    let fullTextContent = originalOptElement.textContent.trim();

                    let folderDisplayName = extractAndNormalizeSearchTerm(fullTextContent);

                    if (!folderDisplayName) {
                        const nameMatch = fullTextContent.match(/^(.+?)(?:\s+\(\d+\))?$/);
                        folderDisplayName = nameMatch && nameMatch[1] ? nameMatch[1].trim() : fullTextContent;
                    }

                    // Create different prefix for thesaurus folders
                    let prefix;
                    if (originalOptElement.dataset.type === 'thesaurus-folder') {
                        console.log('Original folderDisplayName:', folderDisplayName);
                        // For thesaurus folders, extract just the folder name without indentation and item count
                        const cleanName = folderDisplayName.replace(/^\s+/, '').replace(/\s+\[\d+\]$/, '');
                        console.log('Clean name after regex:', cleanName);
                        prefix = cleanName + ": ";
                        console.log('Final prefix:', prefix);
                    } else {
                        prefix = folderDisplayName + ": ";
                    }

                    currentFolderInfo = {
                        id: folderId,
                        name: folderDisplayName,
                        prefix: prefix
                    };
                    lastSelectedFolderInfo = currentFolderInfo;
                    prefixManuallyDeleted = false;
                    isPerformingExternalSearchAction = false;

                    searchInput.value = currentFolderInfo.prefix;
                    searchInput.placeholder = '';

                    const foldersTree = document.querySelector('.folders-tree');
                    if (foldersTree) {
                        foldersTree.querySelectorAll('li.folder-selected').forEach(li => {
                            li.classList.remove('folder-selected');
                        });
                        const treeItem = foldersTree.querySelector(`li[folderid="${folderId}"]`);
                        if (treeItem) {
                            treeItem.classList.add('folder-selected');
                        }
                    }

                    // Handle different folder types
                    if (originalOptElement.dataset.type === 'thesaurus-folder') {
                        // For thesaurus folders, call the thesaurus-specific handler directly
                        selectElement.value = folderId;

                        // Call the thesaurus handler directly
                        if (typeof window.handleThesaurusFolderSelection === 'function') {
                            window.handleThesaurusFolderSelection(folderId);
                        }

                        // Also trigger the change event as backup
                        const event = new Event('change', { bubbles: true });
                        selectElement.dispatchEvent(event);
                    } else if (typeof exploreFolderVitrine === 'function') {
                        exploreFolderVitrine(folderId);
                    } else {
                        console.warn('exploreFolderVitrine function is not defined. Cannot explore folder.');
                    }

                    hasPerformedSearchInCurrentFolder = false;

                    dropdownList.style.display = 'none';

                    const correspondingOption = Array.from(selectElement.options).find(opt => opt.value === folderId && opt.dataset.type !== 'search');
                    if (correspondingOption) {
                        selectElement.value = folderId;
                    }

                    selectElement.removeAttribute('data-user-selected');
                });

                dropdownList.appendChild(optionElement);
                appendedCount++;
            });
        }

        populateDropdown();

        function positionDropdown() {
            const inputRect = searchInput.getBoundingClientRect();
            dropdownList.style.position = 'absolute';
            dropdownList.style.top = (inputRect.bottom + window.scrollY) + 'px';
            dropdownList.style.left = (inputRect.left + window.scrollX) + 'px';
            dropdownList.style.width = inputRect.width + 'px';
            dropdownList.style.zIndex = '1050';

            const noGapContainer = searchInput.closest('.input-group.no-gap');
            const disconnectedContainer = searchInput.closest('.input-group-disconnected');

            if (noGapContainer) {
                const searchButton = noGapContainer.querySelector('.no-gap-btn');
                if (searchButton) {
                    const buttonRect = searchButton.getBoundingClientRect();
                    const adjustedWidth = buttonRect.left - inputRect.left;
                    dropdownList.style.width = adjustedWidth + 'px';
                }
            } else if (disconnectedContainer) {
                // For disconnected layout, use the full width of the input
                dropdownList.style.width = inputRect.width + 'px';
            }
        }

        searchInput.addEventListener('click', function(e) {
            e.stopPropagation();
            const isVisible = dropdownList.style.display === 'block';

            document.querySelectorAll('.custom-select-dropdown').forEach(dropdown => {
                if (dropdown !== dropdownList) {
                    dropdown.style.display = 'none';
                }
            });

            if (!isVisible) {
                positionDropdown();
                dropdownList.style.display = 'block';
                populateDropdown(this.value);
                this.focus();
            }
        });

        window.addEventListener('resize', function() {
            if (dropdownList.style.display === 'block') {
                positionDropdown();
            }
        });

        searchInput.addEventListener('input', function() {
            if (isPerformingExternalSearchAction) {
                return;
            }

            clearTimeout(debounceTimeout);

            if (prefixManuallyDeleted) {
                prefixManuallyDeleted = false;
            }

            let currentValForFiltering = this.value;

            if (!isSimpleMode) {
                if (currentFolderInfo && currentFolderInfo.prefix &&
                    this.value !== '' &&
                    !this.value.startsWith(currentFolderInfo.prefix) &&
                    !prefixManuallyDeleted) {

                    let actualContent = this.value;
                    const parts = this.value.match(/^(.*?:\s)?(.*)$/);
                    if (parts && parts[2]) {
                        actualContent = parts[2];
                    }
                    this.value = currentFolderInfo.prefix + actualContent;
                    currentValForFiltering = this.value;
                    const pos = this.value.length;
                    this.setSelectionRange(pos, pos);

                } else if ((!currentFolderInfo || !currentFolderInfo.prefix) &&
                           this.value.includes(': ') &&
                           !prefixManuallyDeleted) {
                    const valOldPrefixIndex = this.value.lastIndexOf(": ");
                    if (valOldPrefixIndex !== -1) {
                        const textAfterStripping = this.value.substring(valOldPrefixIndex + 2);
                        const currentPlaceholder = searchInput.placeholder || '';
                        if (textAfterStripping !== currentPlaceholder || currentPlaceholder === '') {
                            this.value = textAfterStripping;
                            currentValForFiltering = this.value;
                            const pos = this.value.length;
                            this.setSelectionRange(pos, pos);
                        }
                    }
                }
            }

            selectElement.removeAttribute('data-user-selected');
            const searchActionOption = Array.from(selectElement.options).find(opt => opt.dataset.type === 'search' || opt.value === 'search');

            const isEffectivelyEmpty = currentValForFiltering === '' || (currentFolderInfo && currentValForFiltering === currentFolderInfo.prefix);

            if (isEffectivelyEmpty) {
                if (searchActionOption && selectElement.value !== searchActionOption.value) {
                    selectElement.value = searchActionOption.value;
                    const event = new Event('change', { bubbles: true });
                    selectElement.dispatchEvent(event);
                }
                dropdownList.style.display = 'none';
            } else if (currentFolderInfo && currentValForFiltering === currentFolderInfo.prefix && hasPerformedSearchInCurrentFolder) {
                if (typeof exploreFolderVitrine === 'function') {
                    exploreFolderVitrine(currentFolderInfo.id);
                    hasPerformedSearchInCurrentFolder = false;
                }
                dropdownList.style.display = 'none';
                return;
            }

            if (!isEffectivelyEmpty) {
                populateDropdown(currentValForFiltering);

                const hasContentToShow = dropdownList.querySelector('.custom-select-option');
                if (hasContentToShow) {
                    if (dropdownList.style.display !== 'block') {
                        positionDropdown();
                        dropdownList.style.display = 'block';
                    }
                } else {
                    dropdownList.style.display = 'none';
                }
            } else {
                populateDropdown(currentValForFiltering);
                const hasContentToShow = dropdownList.querySelector('.custom-select-option');
                 if (hasContentToShow && currentValForFiltering.trim() !== '') {
                    if (dropdownList.style.display !== 'block') {
                        positionDropdown();
                        dropdownList.style.display = 'block';
                    }
                } else {
                    dropdownList.style.display = 'none';
                }
            }
        });

        // backspace handling for prefix
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && !isSimpleMode &&
                currentFolderInfo && currentFolderInfo.prefix &&
                this.value.startsWith(currentFolderInfo.prefix) &&
                currentFolderInfo.prefix !== '') {

                if (this.selectionStart <= currentFolderInfo.prefix.length) {
                    e.preventDefault();
                    const textAfterPrefix = this.value.substring(currentFolderInfo.prefix.length);
                    this.value = textAfterPrefix;
                    prefixManuallyDeleted = true;
                    lastSelectedFolderInfo = null;

                    const foldersTree = document.querySelector('.folders-tree');
                    if (foldersTree) {
                        const selectedFolderElement = foldersTree.querySelector('li.folder-selected');
                        if (selectedFolderElement) {
                            if (typeof clearTreeSelection === 'function') {
                                clearTreeSelection();
                            } else {
                                selectedFolderElement.classList.remove('folder-selected');
                            }
                        }
                    }
                    updateSearchPlaceholderAndPrefix();
                    populateDropdown(this.value);
                    return;
                }
            }

            const options = dropdownList.querySelectorAll('.custom-select-option:not(.no-results)');
            const selectedIndex = Array.from(options).findIndex(opt => opt.classList.contains('keyboard-selected'));

            // Helper function to scroll the highlighted option into view
            function scrollHighlightedOptionIntoView() {
                const highlightedOption = dropdownList.querySelector('.custom-select-option.keyboard-selected');
                if (highlightedOption && dropdownList.style.display === 'block') {
                    highlightedOption.scrollIntoView({
                        behavior: 'auto',
                        block: 'nearest',
                        inline: 'nearest'
                    });
                }
            }

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    if (dropdownList.style.display !== 'block') {
                        positionDropdown();
                        dropdownList.style.display = 'block';
                        populateDropdown(this.value);
                    }

                    options.forEach(opt => opt.classList.remove('keyboard-selected'));

                    if (selectedIndex < 0 || selectedIndex >= options.length - 1) {
                        options[0]?.classList.add('keyboard-selected');
                    } else {
                        options[selectedIndex + 1]?.classList.add('keyboard-selected');
                    }

                    // Scroll the highlighted option into view
                    scrollHighlightedOptionIntoView();
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    options.forEach(opt => opt.classList.remove('keyboard-selected'));

                    if (selectedIndex <= 0) {
                        options[options.length - 1]?.classList.add('keyboard-selected');
                    } else {
                        options[selectedIndex - 1]?.classList.add('keyboard-selected');
                    }

                    // Scroll the highlighted option into view
                    scrollHighlightedOptionIntoView();
                    break;

                case 'Enter':
                    e.preventDefault();
                    const selectedKeyboardOption = dropdownList.querySelector('.custom-select-option.keyboard-selected');
                    if (selectedKeyboardOption) {
                        const originalOpt = originalOptions.find(opt => opt.value === selectedKeyboardOption.dataset.value);
                        if (originalOpt && (originalOpt.dataset.type === 'search' || originalOpt.value === 'search')) {
                            initiateMainSearch();
                        } else {
                            selectedKeyboardOption.click();
                        }
                    } else {
                        const trimmedValue = searchInput.value.trim();
                        const hasValidSearchTerm = trimmedValue.length > 0 &&
                            (!currentFolderInfo || !currentFolderInfo.prefix || trimmedValue !== currentFolderInfo.prefix.trim());

                        if (hasValidSearchTerm) {
                            initiateMainSearch();
                        }
                    }
                    break;

                case 'Escape':
                    e.preventDefault();
                    dropdownList.style.display = 'none';
                    break;
            }
        });

        // Prevent cursor placement within prefix
        searchInput.addEventListener('click', function(e) {
            if (currentFolderInfo && currentFolderInfo.prefix &&
                this.value.startsWith(currentFolderInfo.prefix) &&
                this.selectionStart < currentFolderInfo.prefix.length) {

                setTimeout(() => {
                    this.setSelectionRange(currentFolderInfo.prefix.length, currentFolderInfo.prefix.length);
                }, 0);
            }
        });

        // Also handle arrow key navigation to prevent cursor from going into prefix
        searchInput.addEventListener('keydown', function(e) {
            if ((e.key === 'ArrowLeft' || e.key === 'Home') &&
                currentFolderInfo && currentFolderInfo.prefix &&
                this.value.startsWith(currentFolderInfo.prefix)) {

                setTimeout(() => {
                    if (this.selectionStart < currentFolderInfo.prefix.length) {
                        this.setSelectionRange(currentFolderInfo.prefix.length, currentFolderInfo.prefix.length);
                    }
                }, 0);
            }
        }, true);

        document.addEventListener('click', function(e) {
            if (!dropdownList.contains(e.target) && e.target !== searchInput) {
                dropdownList.style.display = 'none';

                if (isSimpleMode) {
                    if (searchInput.value.trim() === '') {
                        searchInput.value = '';
                        if (selectElement.selectedIndex !== -1) {
                            selectElement.selectedIndex = -1;
                            const event = new Event('change', { bubbles: true });
                            selectElement.dispatchEvent(event);
                        }
                        return;
                    }

                    const currentInputText = searchInput.value.trim();
                    const matchingOption = originalOptions.find(opt => opt.textContent.trim().toLowerCase() === currentInputText.toLowerCase());

                    if (matchingOption) {
                        searchInput.value = matchingOption.textContent.trim();
                        if (selectElement.value !== matchingOption.value) {
                            selectElement.value = matchingOption.value;
                            const event = new Event('change', { bubbles: true });
                            selectElement.dispatchEvent(event);
                        }
                    } else {
                        const selectedOption = originalOptions.find(opt => opt.value === selectElement.value);
                        if (selectedOption) {
                            searchInput.value = selectedOption.textContent.trim();
                        } else {
                            searchInput.value = '';
                            if (selectElement.selectedIndex !== -1) {
                                selectElement.selectedIndex = -1;
                                const event = new Event('change', { bubbles: true });
                                selectElement.dispatchEvent(event);
                            }
                        }
                    }
                    return;
                }

                const isUserSelected = selectElement.hasAttribute('data-user-selected');

                if (isUserSelected) {
                    const selectedOptionData = originalOptions.find(opt => opt.value === selectElement.value);
                    if (selectedOptionData) {
                        searchInput.value = (currentFolderInfo ? currentFolderInfo.prefix : '') + selectedOptionData.textContent.trim();
                    }
                } else {
                    updateSearchPlaceholderAndPrefix();
                }
            }
        });

        selectElement.addEventListener('change', function() {
            if (isSimpleMode) return;
            const isUserSelected = this.hasAttribute('data-user-selected');
            const selectedOptionData = originalOptions.find(opt => opt.value === this.value);

            if (isUserSelected && selectedOptionData) {
                updateSearchPlaceholderAndPrefix(selectedOptionData.textContent.trim());
                this.removeAttribute('data-user-selected');
            } else if (selectedOptionData && (selectedOptionData.dataset.type === 'search' || selectedOptionData.value === 'search')) {
                updateSearchPlaceholderAndPrefix();
            } else if (selectedOptionData && selectedOptionData.dataset.type !== 'search' && !isUserSelected) {
                let newText = selectedOptionData.textContent.trim();
                if (currentFolderInfo && currentFolderInfo.prefix && !prefixManuallyDeleted) {
                    searchInput.value = currentFolderInfo.prefix + newText;
                } else {
                    searchInput.value = newText;
                }
                updateSearchPlaceholderAndPrefix();
            } else if (!selectedOptionData) {
                updateSearchPlaceholderAndPrefix();
            }
        });

        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    const removedNodes = Array.from(mutation.removedNodes);
                    const wasSelectRemoved = removedNodes.some(node =>
                        node === selectElement ||
                        (node.contains && node.contains(selectElement))
                    );

                    if (wasSelectRemoved) {
                        if (document.body.contains(dropdownList)) {
                            document.body.removeChild(dropdownList);
                        }
                        observer.disconnect();
                    }
                }
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        // Listen for folder selection changes
        const foldersTreeContainerInstance = document.querySelector('.folders-tree');
        if (!isSimpleMode && foldersTreeContainerInstance) {
            // Listen for the custom folder selection event
            document.addEventListener('folderSelectionChanged', function(event) {
                const previousFolderId = currentFolderInfo ? currentFolderInfo.id : null;
                const newInfo = getSelectedFolderInfo();

                // If the event specifically indicates folderId is null, clear folder info
                if (event.detail && event.detail.folderId === null) {
                    currentFolderInfo = null;
                    lastSelectedFolderInfo = null;
                }

                if (!newInfo || (newInfo && newInfo.id !== previousFolderId)) {
                    hasPerformedSearchInCurrentFolder = false;
                }
                prefixManuallyDeleted = false;
                updateSearchPlaceholderAndPrefix();
            });

            const folderObserver = new MutationObserver(function(mutationsList, observer) {
                for(const mutation of mutationsList) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        if (mutation.target.nodeName === 'LI' && (mutation.target.classList.contains('folder-selected') ||
                            (mutation.oldValue && mutation.oldValue.includes('folder-selected') && !mutation.target.classList.contains('folder-selected')))) {

                            const previousFolderId = currentFolderInfo ? currentFolderInfo.id : null;
                            const newInfo = getSelectedFolderInfo();
                            if (!newInfo || (newInfo && newInfo.id !== previousFolderId)) {
                                hasPerformedSearchInCurrentFolder = false;
                            }
                            prefixManuallyDeleted = false;
                            updateSearchPlaceholderAndPrefix();
                            break;
                        }
                    } else if (mutation.type === 'childList' && mutation.target.closest && mutation.target.closest('.folders-tree')) {
                         let relevantChange = false;
                         mutation.addedNodes.forEach(node => {
                            if (node.nodeName === 'LI' || (node.querySelector && node.querySelector('li.folder-selected'))) relevantChange = true;
                         });
                         mutation.removedNodes.forEach(node => {
                            if (node.nodeName === 'LI' || (node.querySelector && node.querySelector('li.folder-selected'))) relevantChange = true;
                         });
                         if(relevantChange) {
                            const previousFolderId = currentFolderInfo ? currentFolderInfo.id : null;
                            const newInfo = getSelectedFolderInfo();
                             if (!newInfo || (newInfo && newInfo.id !== previousFolderId)) {
                                hasPerformedSearchInCurrentFolder = false;
                            }
                            prefixManuallyDeleted = false;
                            updateSearchPlaceholderAndPrefix();
                         }
                    }
                }
            });

            folderObserver.observe(foldersTreeContainerInstance, {
                attributes: true,
                attributeOldValue: true,
                childList: true,
                subtree: true
            });
        }

        selectElement.dispatchEvent(new CustomEvent('searchable-select-ready', {
            detail: {
                customInputElement: searchInput,
                dropdownListElement: dropdownList,
                originalSelectElement: selectElement
            },
            bubbles: true
        }));
    }

    window.initSearchableDropdowns = initSearchableDropdowns;
    window.makeDropdownSearchable = makeDropdownSearchable;

    document.addEventListener('DOMContentLoaded', initSearchableDropdowns);
})();
