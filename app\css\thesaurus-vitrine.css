#main-ul, .thesaurus-tree:not(.folders-tree) {
    display: none !important;
}

/* Layout styles */
#menuGauche {
    display: flex;
    flex-direction: column;
    height: 79vh;
}

#left-top {
    transition: all 0.3s ease;
}

#left-top.hidden {
    display: none !important;
}

#left-bot {
    display: none; /* Hidden by default */
    overflow-y: hidden;
    min-height: 100px;
    transition: all 0.3s ease;
}

#left-bot.expanded {
    display: flex !important; /* Show when expanded */
    flex-direction: column;
    max-height: none;
    min-height: 100%;
    flex: 1;
    height: 100%;
}

/* Tree container styles */
#dynamic-thesaurus-tree {
    min-height: 50px;
    padding: 10px;
    height: 100%;
}

#dynamic-thesaurus-tree.expanded {
    height: 100%;
    flex: 1;
}

/* Custom thesaurus tree styles */
.thesaurus-folders-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.thesaurus-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
}

.thesaurus-tree-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: visible;
    height: 100%;
    min-height: 0;
}

#thesaurus-folders-tree {
    height: 100%;
    overflow-y: auto;
    overflow-x: visible;
    padding: 10px 5px;
}

/* Force initial collapsed state for thesaurus tree */
#thesaurus-folders-tree .folder-children:not(.active) {
    display: none !important;
    visibility: hidden !important;
}

#thesaurus-folders-tree .folder-children.active {
    display: block !important;
    visibility: visible !important;
}

/* Back button styles */
#back-to-thesaurus-btn {
    display: none;
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #495057;
    text-decoration: none;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

#back-to-thesaurus-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
}

#back-to-thesaurus-btn.show {
    display: block;
}

/* Thesaurus list styles */
.list-group-item.MainPageThesaurus {
    margin-bottom: 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.list-group-item.MainPageThesaurus:hover {
    background-color: #f8f9fa;
}

/* Ensure thesaurus tree inherits folder styling */
#dynamic-thesaurus-tree .folders-tree {
    /* Inherits all folder styling from folders-tree-vitrine.css */
}