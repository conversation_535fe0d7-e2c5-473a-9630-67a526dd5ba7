let totalPage = 0;
const totalPagePactols = 0;
const no_results_en = "No results";
const no_results_fr = "Pas de résultats";

function showThesaurusTree() {
    const leftTop = document.getElementById('left-top');
    const leftBot = document.getElementById('left-bot');
    const backButton = document.getElementById('back-to-thesaurus-btn');
    const dynamicTree = document.getElementById('dynamic-thesaurus-tree');

    if (leftTop) leftTop.classList.add('hidden');
    if (leftBot) leftBot.classList.add('expanded');
    if (backButton) backButton.classList.add('show');
    if (dynamicTree) dynamicTree.classList.add('expanded');
}

function hideThesaurusTree() {
    const leftTop = document.getElementById('left-top');
    const leftBot = document.getElementById('left-bot');
    const backButton = document.getElementById('back-to-thesaurus-btn');
    const dynamicTree = document.getElementById('dynamic-thesaurus-tree');
    const menuCentre = document.getElementById('menuCentre');

    if (leftTop) leftTop.classList.remove('hidden');
    if (leftBot) leftBot.classList.remove('expanded');
    if (backButton) backButton.classList.remove('show');
    if (dynamicTree) {
        dynamicTree.classList.remove('expanded');
        dynamicTree.innerHTML = '';
    }
    if (menuCentre) menuCentre.style.display = 'none';

    // Clean up thesaurus-specific event handlers
    $(document).off('click.thesaurusTree mouseover.thesaurusHover mouseout.thesaurusHover');
}

// Helper function to render tree HTML directly
function renderThesaurusTreeHTML(tree) {
    if (!tree || tree.length === 0) {
        return '<div class="thesaurus-folders-container"><div class="thesaurus-section"><div class="thesaurus-tree-container"><ul id="thesaurus-folders-tree" class="folders-tree pt-1 px-2"><li><span>No thesaurus items available</span></li></ul></div></div></div>';
    }

    const userRead = window.SERVER_DATA ? window.SERVER_DATA.USER_READ : [];
    const userStatus = window.SERVER_DATA ? window.SERVER_DATA.USER_STATUS : 'guest';
    const lng = window.SERVER_DATA ? window.SERVER_DATA.LNG : 'en';

    function hasAccess(folderId) {
        return Array.isArray(userRead) && userRead.indexOf(folderId) !== -1 || userStatus === 'admin';
    }

    function renderFolderHTML(folder, level = 0) {
        if (!hasAccess(folder.id)) return '';

        const hasChildren = folder.children && folder.children.length > 0;
        const folderName = folder.name || folder.folder_name || `Item ${folder.id}`;
        const immediateCount = folder.nb_images || 0;
        const totalCount = folder.nb_tot_images || folder.nb_tot_items || immediateCount;

        let html = `<li folderId="${folder.id}" rank="${folder.global_rank || ''}" data-level="${level}">`;
        html += '<div class="d-flex align-items-center">';

        if (hasChildren) {
            const isInitiallyExpanded = folder.initiallyExpanded === true;
            const parentClass = isInitiallyExpanded ? 'folder-parent folder-parent-down' : 'folder-parent';
            html += `<span class="${parentClass}"></span>`;
        } else {
            html += '<span class="folder-spacer"></span>';
        }

        html += '<div class="folder-name-container">';
        html += `<span class="folder-name" title="${folderName}" data-full-name="${folderName}">`;
        html += `<i class="fas fa-folder"></i> ${folderName}</span>`;
        html += '<span class="folder-number">';

        if (immediateCount > 0) {
            html += `[${immediateCount}]`;
            if (totalCount !== immediateCount && totalCount > 0) {
                html += `(${totalCount})`;
            }
        } else if (totalCount > 0) {
            html += `(${totalCount})`;
        }

        html += '</span></div></div>';

        if (hasChildren) {
            const isInitiallyExpanded = folder.initiallyExpanded === true;
            const childrenClass = isInitiallyExpanded ? 'folder-children active' : 'folder-children';
            const childrenStyle = isInitiallyExpanded ? 'display: block;' : 'display: none;';
            html += `<ul class="${childrenClass}" style="${childrenStyle}">`;
            folder.children.forEach(child => {
                html += renderFolderHTML(child, level + 1);
            });
            html += '</ul>';
        }

        html += '</li>';
        return html;
    }

    let html = '<div id="thesaurus-folders-container" class="thesaurus-folders-container">';
    html += '<div class="thesaurus-section"><div class="thesaurus-tree-container">';
    html += `<ul id="thesaurus-folders-tree" class="folders-tree pt-1 px-2" data-user-status="${userStatus}" data-lng="${lng}">`;

    tree.forEach(folder => {
        html += renderFolderHTML(folder);
    });

    html += '</ul></div></div></div>';
    return html;
}

// Simple tree rendering using existing component
function renderThesaurusTree(treeData, thesaurus, type) {
    const container = document.getElementById('dynamic-thesaurus-tree');
    if (!container) return;

    if (!treeData || treeData.length === 0) {
        container.innerHTML = '<div class="alert alert-info">No thesaurus tree data available</div>';
        return;
    }

    const folderMap = {};
    const rootFolders = [];

    // First pass: create folder objects
    treeData.forEach(item => {
        folderMap[item.id] = {
            id: item.id,
            name: item.name || item.short_name || `Item ${item.id}`,
            folder_name: item.name || item.short_name || `Item ${item.id}`,
            nb_images: item.nb_item || 0,
            nb_objects: 0,
            nb_unicos: 0,
            nb_tot_images: item.nb_tot_item || item.nb_item || 0,
            nb_tot_objects: 0,
            nb_tot_unicos: 0,
            nb_tot_items: item.nb_tot_item || item.nb_item || 0,
            virtual: 0,
            id_parent: item.id_parent,
            depth: item.depth,
            global_rank: item.global_rank || 0,
            children: []
        };
    });

    // Second pass: build parent-child relationships
    treeData.forEach(item => {
        const folderObj = folderMap[item.id];
        if (!folderObj) return;

        if (!item.id_parent) {
            // Root level folder
            rootFolders.push(folderObj);
        } else if (folderMap[item.id_parent]) {
            // Add to parent's children array
            folderMap[item.id_parent].children.push(folderObj);
        } else {
            // Parent not found, treat as root level
            rootFolders.push(folderObj);
        }
    });

    const transformedTree = rootFolders;



    // Render directly using the new folders-only component
    const templateData = {
        tree: transformedTree,
        user: {
            user_status: window.SERVER_DATA ? window.SERVER_DATA.USER_STATUS : 'guest',
            read: window.SERVER_DATA ? window.SERVER_DATA.USER_READ : [],
            write: window.SERVER_DATA ? window.SERVER_DATA.USER_WRITE : []
        },
        selectedFolder: null,
        lng: window.SERVER_DATA ? window.SERVER_DATA.LNG : 'en',
        projectId: window.SERVER_DATA ? window.SERVER_DATA.PROJECT_ID : ''
    };

    const treeHtml = renderThesaurusTreeHTML(transformedTree);
    container.innerHTML = treeHtml;

    // Populate the searchable dropdown with thesaurus data
    populateThesaurusDropdown(transformedTree);

    // Ensure folder children start collapsed IMMEDIATELY after rendering (except first folder)
    $('#thesaurus-folders-tree .folder-children:not(.active)').removeClass('active').hide().css('display', 'none');
    $('#thesaurus-folders-tree .folder-parent:not(.folder-parent-down)').removeClass('folder-parent-down');
    $('.folder-children:not(.active)').removeClass('active').hide();
    $('.folder-parent:not(.folder-parent-down)').removeClass('folder-parent-down');

    // Initialize thesaurus tree with specialized handlers
    setTimeout(() => {
        initThesaurusTreeHandlers(thesaurus, type);
    }, 100);
}

// Function to populate the searchable dropdown with thesaurus data
function populateThesaurusDropdown(tree) {
    const dropdown = document.getElementById('thesaurus-folder-dropdown');
    const dropdownContainer = document.getElementById('thesaurus-dropdown-container');

    if (!dropdown || !dropdownContainer) return;

    // Clear existing options except the search option
    const searchOption = dropdown.querySelector('option[data-type="search"]');
    dropdown.innerHTML = '';
    if (searchOption) {
        dropdown.appendChild(searchOption);
    }

    // Flatten the tree structure to get all folders
    function flattenTree(folders, level = 0) {
        let flatList = [];
        folders.forEach(folder => {
            if (folder.nb_tot_items > 0) { // Only include folders with items
                const indent = '  '.repeat(level);
                const folderName = folder.name || folder.folder_name || `Item ${folder.id}`;
                const displayName = `${indent}${folderName} [${folder.nb_tot_items}]`;

                flatList.push({
                    id: folder.id,
                    name: displayName,
                    level: level
                });
            }

            if (folder.children && folder.children.length > 0) {
                flatList = flatList.concat(flattenTree(folder.children, level + 1));
            }
        });
        return flatList;
    }

    const flatFolders = flattenTree(tree);

    // Add options to dropdown
    flatFolders.forEach(folder => {
        const option = document.createElement('option');
        option.value = folder.id;
        option.textContent = folder.name;
        option.dataset.type = 'thesaurus-folder';
        dropdown.appendChild(option);
    });

    // Show the dropdown container if we have folders
    if (flatFolders.length > 0) {
        dropdownContainer.style.display = 'block';

        // Initialize the searchable dropdown if not already done
        setTimeout(() => {
            if (window.makeDropdownSearchable && !dropdown.classList.contains('searchable-initialized')) {
                window.makeDropdownSearchable(dropdown);

                // Add custom handler for thesaurus folder selection
                setupThesaurusDropdownHandler(dropdown);
            }
        }, 100);
    } else {
        dropdownContainer.style.display = 'none';
    }
}

// Function to set up custom handler for thesaurus dropdown selections
function setupThesaurusDropdownHandler(dropdown) {
    // Store the current thesaurus and type for later use
    let currentThesaurus = null;
    let currentType = null;

    // Listen for changes on the dropdown
    dropdown.addEventListener('change', function(e) {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption && selectedOption.dataset.type === 'thesaurus-folder') {
            const folderId = selectedOption.value;

            // Get current thesaurus info from the page context
            const activeThesaurus = document.querySelector('.MainPageThesaurus.active');
            if (activeThesaurus) {
                currentThesaurus = activeThesaurus.id;
                // Determine type based on onclick attribute
                const onclickAttr = activeThesaurus.getAttribute('onclick');
                if (onclickAttr && onclickAttr.includes('multi')) {
                    currentType = 'multi';
                } else {
                    currentType = 'simple';
                }
            }

            // Select the folder in the tree
            const treeSelector = '#dynamic-thesaurus-tree .folders-tree';
            const $tree = $(treeSelector);
            if ($tree.length) {
                $tree.find('li').removeClass('folder-selected');
                const $targetFolder = $tree.find(`li[folderId="${folderId}"]`);
                if ($targetFolder.length) {
                    $targetFolder.addClass('folder-selected');

                    // Expand parent folders to show the selected folder
                    $targetFolder.parents('.folder-children').each(function() {
                        $(this).addClass('active').show();
                        $(this).siblings('.d-flex').find('.folder-parent').addClass('folder-parent-down');
                    });
                }
            }

            // Explore the thesaurus items
            if (currentThesaurus && currentType) {
                exploreThesaurusItems(currentThesaurus, folderId, currentType);
            }
        }
    });
}

// Comprehensive initialization for thesaurus tree handlers
function initThesaurusTreeHandlers(thesaurus, type) {
    const treeSelector = '#dynamic-thesaurus-tree .folders-tree';

    // Clean up any existing handlers
    $(document).off('click.thesaurusTree mouseover.thesaurusHover mouseout.thesaurusHover');

    // 1. Folder parent expand/collapse handlers
    $(document).on('click.thesaurusTree', `${treeSelector} .folder-parent`, function(e) {
        const $children = $(this).closest('li').children('.folder-children');
        const isOpen = $children.hasClass('active');

        if (isOpen) {
            $(this).removeClass('folder-parent-down');
            $children.removeClass('active');
            $children.hide();
        } else {
            $(this).addClass('folder-parent-down');
            $children.addClass('active');
            $children.show();
        }

        e.stopPropagation();
        e.preventDefault();
    });

    // 2. Folder name, number, and container click handlers for expansion and exploration
    $(document).on('click.thesaurusTree', `${treeSelector} .folder-name, ${treeSelector} .folder-number, ${treeSelector} .folder-name-container`, function(e) {
        const $li = $(this).closest('li');
        const folderId = $li.attr('folderId');
        if (!folderId) return;

        // Handle folder expansion if it has children
        const $folderParent = $li.find('> .d-flex > .folder-parent');
        const hasChildren = $folderParent.length > 0;

        if (hasChildren) {
            const $children = $li.children('.folder-children');
            const isOpen = $children.hasClass('active');

            if (!isOpen) {
                $folderParent.addClass('folder-parent-down');
                $children.addClass('active');
                $children.show();
            }
        }

        // Handle folder selection visual feedback
        $(`${treeSelector} li`).removeClass('folder-selected');
        $li.addClass('folder-selected');

        // Handle thesaurus item exploration
        const folderNumber = $li.find('.folder-number');
        const numberText = folderNumber.text();
        const hasItems = numberText.includes('[') || numberText.includes('(');

        if (hasItems) {
            exploreThesaurusItems(thesaurus, folderId, type);
        }

        e.preventDefault();
        e.stopPropagation();
    });

    // 3. Hover effects
    let hoverTimer;

    $(document).on('mouseover.thesaurusHover', `${treeSelector} li > .d-flex, ${treeSelector} .folder-name-container`, function(e) {
        const $li = $(this).closest('li');
        clearTimeout(hoverTimer);
        hoverTimer = setTimeout(function() {
            $li.addClass('folder-hover');
        }, 50);

        e.stopPropagation();
    });

    $(document).on('mouseout.thesaurusHover', `${treeSelector} li > .d-flex, ${treeSelector} .folder-name-container`, function(e) {
        clearTimeout(hoverTimer);
        $(this).closest('li').removeClass('folder-hover');
        e.stopPropagation();
    });

    // Also remove hover when leaving the li element itself
    $(document).on('mouseout.thesaurusHover', `${treeSelector} li`, function(e) {
        if (!$(e.relatedTarget).closest('li').is(this)) {
            clearTimeout(hoverTimer);
            $(this).removeClass('folder-hover');
        }
    });

    // 4. Handle clicks on list items themselves (prevent bubbling)
    $(document).on('click.thesaurusTree', `${treeSelector} li`, function(e) {
        if (e.target === this) {
            e.stopPropagation();
        }
    });

    // 5. Clear selection when clicking outside the tree
    $(document).on('click.thesaurusTree', function(e) {
        if (!$(e.target).closest(treeSelector).length) {
            $(`${treeSelector} li`).removeClass('folder-selected');
        }
    });
}

// Function to explore thesaurus items with proper display mode and pagination support
function exploreThesaurusItems(thesaurus, conceptId, type) {

    const projectId = window.SERVER_DATA ? window.SERVER_DATA.PROJECT_ID : '';

    // Set up the explorePage object for proper view switching support
    if (typeof window.explorePage === 'undefined') window.explorePage = {};

    window.explorePage.thesaurus = thesaurus;
    window.explorePage.thesId = conceptId;
    window.explorePage.id = projectId;
    window.explorePage.type = type;
    window.explorePage.thesPath = conceptId;

    // Set up URL function for this exploration type - use dynamic display reference
    if (type === 'multi') {
        window.explorePage.url = function(newPage) {
            const currentDisplay = (typeof display !== 'undefined') ? display : 'grid';
            return `/exploreThesaurusVitrineMultiPage/${thesaurus},${conceptId},${newPage}?display=${currentDisplay}&limit=0`;
        };
    } else if (type === 'pactols') {
        window.explorePage.url = function(newPage) {
            const currentDisplay = (typeof display !== 'undefined') ? display : 'grid';
            return `/exploreThesaurusVitrinePactolsPage/${thesaurus},${conceptId},${newPage}?display=${currentDisplay}&thes_path=${conceptId}&projectId=${projectId}&limit=0`;
        };
    } else {
        window.explorePage.url = function(newPage) {
            const currentDisplay = (typeof display !== 'undefined') ? display : 'grid';
            return `/exploreThesaurusVitrineSimplePage/${thesaurus},${conceptId},${newPage}?display=${currentDisplay}&projectId=${projectId}&limit=0`;
        };
    }

    const exploreDiv = document.getElementById('explore-div');
    const menuCentre = document.getElementById('menuCentre');
    const exploreResults = document.getElementById('explore-results');

    if (exploreDiv) {
        exploreDiv.style.cssText = 'display: flex !important;';
    }

    if (menuCentre) {
        menuCentre.style.display = 'block';
    }

    if (exploreResults) {
        exploreResults.innerHTML = '<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>';
    }

    // Use the URL function to get the correct URL for page 1
    const exploreUrl = window.explorePage.url(1);
    let countUrl;
    if (type === 'multi') {
        countUrl = `/exploreThesaurusVitrineMultiNb/${thesaurus},${conceptId}`;
    } else if (type === 'pactols') {
        countUrl = `/exploreThesaurusVitrinePactolsNb/${thesaurus},${conceptId}?thes_path=${conceptId}&projectId=${projectId}`;
    } else {
        countUrl = `/exploreThesaurusVitrineSimpleNb/${thesaurus},${conceptId}`;
    }

    fetch(exploreUrl)
        .then(response => response.text())
        .then(html => {
            document.getElementById('explore-results').innerHTML = html;

            if (typeof initLazyLoading === 'function') {
                initLazyLoading();
            }

            return fetch(countUrl);
        })
        .then(response => response.json())
        .then(countData => {
            const total = countData.total || countData.nbAccess || 0;
            const totalCount = countData.totalCount || total;
            const restrictedCount = totalCount - total;

            let resultText = `${total} items found`;
            if (restrictedCount > 0) {
                resultText += ` (no perms for ${restrictedCount} files)`;
            }

            const nbResultsElement = document.getElementById('nb-results');
            if (nbResultsElement) {
                nbResultsElement.textContent = resultText;
            }
        })
        .catch(error => {
            console.error('Error loading thesaurus items:', error);
            document.getElementById('explore-results').innerHTML = '<div class="alert alert-danger">Error loading items: ' + error.message + '</div>';
        });
}

// Back button function (exposed globally)
window.showThesaurusListAndCollapseTree = function() {
    hideThesaurusTree();
}

// Essential selection functions for the dropdown
window.checkAll = function() {
    const items = document.querySelectorAll(".check_item:not(.checked)");
    if (items.length === 0) return;

    const itemsToAdd = [];
    const elementsToUpdate = [];

    items.forEach(item => {
        const info = item.id.split("-");
        if (info.length < 3) return;

        const item_type = info[1];
        if (!item_type) return;

        if (!(item_type === "o" || item_type === "i" || item_type === "d" || item_type === "u")) return;

        const parts = info[2].split("_");
        if (parts.length < 2) return;

        const item_id = parts[0];
        const folder_id = parts[1];

        const selected_item = item_type.charAt(0) + item_id + "_" + folder_id;

        itemsToAdd.push(selected_item);
        elementsToUpdate.push({
            element: item,
            checkbox: item.querySelector(".form-check-input")
        });
    });

    requestAnimationFrame(() => {
        elementsToUpdate.forEach(({element, checkbox}) => {
            element.classList.add("checked");
            element.classList.add("see-select");
            if (checkbox && !checkbox.checked) {
                checkbox.checked = true;
            }
        });

        if (typeof selection !== 'undefined') {
            itemsToAdd.forEach(item => selection.add(item));
            localStorage.setItem("selection", JSON.stringify(Array.from(selection)));
        }

        const nbSelectedElement = document.getElementById("nb-selected");
        if (nbSelectedElement) {
            const currentCount = typeof selection !== 'undefined' ? selection.size : itemsToAdd.length;
            nbSelectedElement.innerText = currentCount.toString();
        }

        document.dispatchEvent(new CustomEvent('selectionChanged'));
    });
};

window.uncheckAll = function() {
    const items = document.querySelectorAll(".check_item.checked");
    if (items.length === 0) return;

    const itemsToRemove = [];
    const elementsToUpdate = [];

    items.forEach(item => {
        const info = item.id.split("-");
        if (info.length < 3) return;

        const item_type = info[1];
        if (!item_type) return;

        if (!(item_type === "o" || item_type === "i" || item_type === "d" || item_type === "u")) return;

        const parts = info[2].split("_");
        if (parts.length < 2) return;

        const item_id = parts[0];
        const folder_id = parts[1];

        const selected_item = item_type.charAt(0) + item_id + "_" + folder_id;

        itemsToRemove.push(selected_item);
        elementsToUpdate.push({
            element: item,
            checkbox: item.querySelector(".form-check-input")
        });
    });

    requestAnimationFrame(() => {
        elementsToUpdate.forEach(({element, checkbox}) => {
            element.classList.remove("checked");
            element.classList.remove("see-select");
            if (checkbox && checkbox.checked) {
                checkbox.checked = false;
            }
        });

        if (typeof selection !== 'undefined') {
            itemsToRemove.forEach(item => selection.delete(item));
            localStorage.setItem("selection", JSON.stringify(Array.from(selection)));
        }

        const nbSelectedElement = document.getElementById("nb-selected");
        if (nbSelectedElement) {
            const currentCount = typeof selection !== 'undefined' ? selection.size : 0;
            nbSelectedElement.innerText = currentCount.toString();
        }

        document.dispatchEvent(new CustomEvent('selectionChanged'));
    });
};

// Initialize selection Set if not already defined
if (typeof selection === 'undefined') {
    window.selection = new Set();
    try {
        const stored = localStorage.getItem("selection");
        if (stored) {
            const parsed = JSON.parse(stored);
            if (Array.isArray(parsed)) {
                parsed.forEach(item => selection.add(item));
            }
        }
    } catch (e) {
        console.warn('Could not load selection from localStorage:', e);
    }
}

function getThesaurusGlobal() {
  $.ajax({
    url: `/getThesaurusOrigin,${root}`,
  }).done(() => {});
}

function getThesaurusTreePeriodo(branch) {
  let htmltree = "";
  // on commence par vider le tabloid avant d'affichier qqch avec un exploreThesaurus ?
  $("#tabloid").empty();
  $("#tabloid").removeClass("container");
  $("#tabloid").addClass("container-fluid");
  //$('#tabloid').html('<img src="/assets/images/image_'+root+'.jpg" style="max-height:99%;max-width:99%;margin-left:auto;margin-right:auto;display:block;opacity: 0.5;">')
  $("#tabloid").html(
    `<img src="/assets/images/image_${branch}.jpg" style="max-height:99%;max-width:99%;margin-right:auto;display:block;opacity: 0.2;">`
  );

  $(".MainPageThesaurus").removeClass("active");
  $(`#${name}`).addClass("active");

  $(".alert").remove();

  $.ajax({
    url: `/getThesaurusPeriodo/${branch}`,
  }).done((data) => {
    htmltree += '<br><br><ul id="main-ul" style="padding-left:0em!important;">';

    for (let i = 0; i < data.length; i++) {
      htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
      htmltree +=
        `<a href="#" onclick="exploreThesaurus(\'${branch}','periodo',${data[i].id})" style="text-decoration: none;font-weight: bolder;" >${data[i].label} [${data[i].nb_item}]</a></li>`;
    }
    htmltree += "</ul>";
    htmltree += "</div>";
    $("#menuGauche").html(htmltree);
  });
}

function getThesaurusTreeMulti(root, projectId, name, thesId, userId) {
  let htmltree = "";
  // on commence par vider le tabloid avant d'affichier qqch avec un exploreThesaurus ?
  $("#tabloid").empty();
  $("#tabloid").removeClass("container");
  $("#tabloid").addClass("container-fluid");
  //$('#tabloid').html('<img src="/assets/images/image_'+root+'.jpg" style="max-height:99%;max-width:99%;margin-left:auto;margin-right:auto;display:block;opacity: 0.5;">')
  //$('#tabloid').html('<img src="/assets/images/image_'+root+'.jpg" style="max-height:99%;max-width:99%;margin-right:auto;display:block;opacity: 0.2;">')

  $(".MainPageThesaurus").removeClass("active");
  $(".MainPageThesaurusSimple").removeClass("active");
  $(`#${name}`).addClass("active");

  $(".alert").remove();

  $.ajax({
    url: `/getThesaurusTreeDataMulti/${root},${name},${thesId}`,
  }).done((data) => {
    htmltree += '<br><br><ul id="main-ul" style="padding-left:0em!important;">';

    const indice = [];
    const profond = [];
    let ind = 0;
    for (let i = 0; i < data.length; i++) {
      // on ramène tout même ceux qui n'ont pas d'item pour pouvoir récupérer leurs enfants qui ont des items (nb_tot_item != 0)
      if (data[i].depth > 1) {
        // il ne faut pas afficher le thesaurus item racine (ca ne sert à rien)
        if (data[i].get_children === null) {
          if (profond[ind - 1] > data[i].depth) {
            // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
            if (i - indice[ind - 1] > 1) {
              let profondeur2 = profond[ind - 1] - data[i].depth;
              if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
              for (let p = 0; p < profondeur2; p++) {
                htmltree += "</ul></li>";
              }
            }
          }
          // pas d'enfant , regarder le suivant
          for (let j = i; j < data.length; j++) {
            if (j === i + 1) {
              //si c'est la même profondeur : affichage simple
              if (data[i].depth === data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
                else htmltree += data[i].name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li>";
              }
              // sinon, on change de profondeur, il faut refermer la liste
              else if (data[i].depth > data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item) {
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
                } else htmltree += data[i].name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li> ";
                // si on remonte de plus de 1 niveau , il faut fermer les niveaux intermédiaires
                if (data[i].depth - data[j].depth > 1) {
                  //il faut fermer autant de fois qu'il y a de différence entre les 2 niveaux
                  for (let k = data[j].depth; k < data[i].depth; k++) {
                    htmltree += "</ul></li>";
                  }
                  // enfin on referme le dernier niveau
                } else {
                  htmltree += "</ul></li>";
                }
              }
            }
          }
          // si c'est le dernier (pas de suivant !)
          if (i === data.length - 1) {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
            if (data[i].nb_item) {
              htmltree +=
                `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
            } else htmltree += data[i].name;
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "</li>";
            // TODO test si c'est le seul (data[i-1] undefined!!!)
            if (typeof data[i - 1] !== "undefined") {
              if (data[i].depth !== data[i - 1].depth) {
                //On regarde la profondeur de celui d'avant
                const profondeur = data[i].depth;
                for (let pr = 0; pr < profondeur; pr++) {
                  console.log(pr);
                  htmltree += "</ul></li>";
                }
              }
            }
          }
        }
        // enfants : on met en place l'arborescence
        else {
          if (i === 0) {
            htmltree +=
              `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
            if (data[i].nb_tot_item) {
              if (data[i].nb_item)
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name} [${data[i].nb_item}]`;
              else
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name}`;
              //if (data[i]['nb_item'] != 0) htmltree += ' [' + data[i]['nb_item'] + ']'
              htmltree += ` (${data[i].nb_tot_item})</label></a>`;
            }
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "<ul>";
          } else {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            for (let j = i; j < data.length; j++) {
              // en regardant le suivant
              if (j === i + 1) {
                // le suivant sera visible on mets en place l'arborescence
                // Mais erreurs dans le cas ou le précédent visible était d'une depth plus petite :
                // on n'a pas refermée le ul .......
                htmltree +=
                  `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name} [${data[i].nb_item}]`;
                else
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusMultiPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId}, 0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name}`;
                //if (data[i]['nb_item'] != 0) htmltree += ' [' + data[i]['nb_item'] + ']'
                if (data[i].nb_tot_item) htmltree += ` (${data[i].nb_tot_item})`;
                htmltree += "</label></a>";
                //}
                // si nb tot images est null c'est qu'il n'y a aucune image ni dans le dossier ni plus bas dans l'arborescence
                //else htmltree += data[i].short_name
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "<ul>";
              }
            }
          }
        }
      }
    }
    htmltree += "</ul>";
    //htmltree += '</div>'
    $("#menuGauche").html(htmltree);
  });
}

function getThesaurusDetails(thesId) {}

function CompleteexploreThesaurus(root, thesaurus, idThes) {
  let html = "";
  $("#tabloid").empty();
  $(".alert").remove();

  $.ajax({
    url: `/exploreThes,${root},${thesaurus},${idThes}`,
  }).done((data) => {
    if (typeof data[0] !== "undefined") {
      // ON FAIT DIFFEREMENT que avec les explore de fichiers: on n'a pas de visionneuse,
      // on renvoie simplement sur la page de l'item en question  ?
      //
      // récupérer le nom du thésaurus pour toujours savoir ce que l'on affiche
      html +=
        `<div class="d-flex justify-content-between" id="topexplore"><span style="font-size: 1.6em">${thesaurus} > ${data[0].short_name}</span><a href="#image_${data[0].id}" data-toggle="modal" title="Visionneuse"><i class="fas fa-images fa-2x"></i></a></div>`;
      //html += '<div class="card-columns"> '
      html += '<br><div class="listTabloid">';
      for (let i = 0; i < data.data_length; i++) {
        if (data[i].extension === "3d") {
          html +=
            `<div class="card"><a href="/viewer3d,${data[i].srcImg3d}" title="The 3D viewer" target="_blank"><img class="card-img mx-auto d-block" src="/media/image?fid=${data[i].idfolder}&id=${data[i].id}&format=${data[i].srcImgThumb}&type=thumb&root=${root}" alt="${data[i].filename}"></a>`;

          html +=
            `<div class="card-body"><div class="card-text"><div class="d-flex justify-content-between"> <span id="${root}_${data[i].id}"><a href="/edit,${root},${data[i].model},file,${data[i].id},${data[i].idfolder}" title=""Indexer" ><i class="far fa-file-alt" style="margin: 0 0.5em"></i></span><small class="text-muted">${data[i].filename}</small></div></div></div></div>`;
        } else {
          html +=
            `<div class="card"><a href="#image_${data[i].id}" data-toggle="modal" ><img class="card-img mx-auto d-block" src="/media/image?fid=${data[i].idfolder}&id=${data[i].id}&format=${data[i].srcImgThumb}&type=thumb&root=${root}" alt="${data[i].filename}"></a>`;

          html += '<div class="card-body">' + '<div class="card-text">' + '<div class="d-flex justify-content-between"> ';

          html +=
            `<span id="${root}_${data[i].id}"></span><a href="/visionneuse,${data[i].id}-${data[i].idfolder},${root}-p" class="mx-auto" target="_blank" title="Ouvrir dans un nouvel onglet"><i class="fas fa-external-link-alt"></i></a><a href="/edit,${root},${data[i].model},file,${data[i].id},${data[i].idfolder}" title="Indexer" ><i class="far fa-file-alt" style="margin: 0 0.5em"></i></a></div></div></div></div>`;
        }
      }

      html += "</div></div>";

      // mise en place de la visionneuse modale (autant de visionneuse que de contenu...)
      for (let i = 0; i < data.data_length; i++) {
        html +=
          `<div class="modal" id="image_${data[i].id}" role="dialog"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal">x</button><h4>${thesaurus}</h4><a href="#" onclick="switchModal(\'text\', ${data[i].id},${data[i].idfolder},'${root}')\" title="metadata" ><i class="fas fa-file-alt fa-2x"></i></a></div><div class="modal-body" style="display:flex; align-items:center"><a id="a-card-img-center" href="/media/image?fid=${data[i].idfolder}&id=${data[i].id}&format=${data[i].srcImgHd}&type=hd&root=${root}"  onclick=\"window.open(this.href, 'photo', 'status=no, resizable=yes, scrollbars=yes');return false;\" >`;
          html+= `<img class="mx-auto d-block" src="/media/image?fid=${data[i].idfolder}&id=${data[i].id}`;
        if (data[i].foldername === "unicos") html += `&format=${data[i].srcImgThumb}&type=thumb&root=${root}"`;
        else html += `&format=${data[i].srcImgSmall}&type=small&root=${root}"`;
        html += ` alt="${data[i].filename}"></a></div><div class="modal-footer">`;

        if (i === 0) {
          if (data.data_length > 1) {
            html +=
              `<p style="text-align: center;width: 100%;">${data[i].filename}</p><button type="button" class="btn btn-primary" onclick="showPrevNextModal(${data[i].id},${data[i + 1].id},'image',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-right"></i></button>`;
          }
        } else if (i === data.data_length - 1) {
          html +=
            `<button type="button" class="btn btn-primary mr-auto" onclick="showPrevNextModal(${data[i].id},${data[i - 1].id},'image',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-left"></i></button><p style="text-align: center;width: 100%;">${data[i].filename}</p>`;
        } else {
          html +=
            `<button type="button" class="btn btn-primary mr-auto" onclick="showPrevNextModal(${data[i].id},${data[i - 1].id},'image',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-left"></i></button><p style="text-align: center;width: 100%;">${data[i].filename}</p><button type="button" class="btn btn-primary" onclick="showPrevNextModal(${data[i].id},${data[i + 1].id},'image',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-right"></i></button>`;
        }
        html += "</div></div></div></div>";
      }

      //Une 2eme modale pour le contenu metadata
      for (let i = 0; i < data.data_length; i++) {
        html +=
          `<div class="modal" id="text_${data[i].id}" role="dialog" aria-hidden="true" ><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal">x</button><h4>${thesaurus}</h4><a href="#" onclick="switchModal(\'image\', ${data[i].id},${data[i].idfolder},'${root}')\" title="image" ><i class="far fa-file-image fa-2x"></i></a></div><div class="modal-body"></div><div class="modal-footer">`;
        if (i === 0) {
          if (data.data_length > 1) {
            html +=
              `<p style="text-align: center;width: 100%;">${data[i].filename}</p><button type="button" class="btn btn-primary" onclick="showPrevNextModal(${data[i].id},${data[i + 1].id}, 'text',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-right"></i></button>`;
          }
        } else if (i === data.data_length - 1) {
          html +=
            `<button type="button" class="btn btn-primary mr-auto" onclick="showPrevNextModal(${data[i].id},${data[i - 1].id}, 'text',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-left"></i></button><p style="text-align: center;width: 100%;">${data[i].filename}</p>`;
        } else {
          html +=
            `<button type="button" class="btn btn-primary mr-auto" onclick="showPrevNextModal(${data[i].id},${data[i - 1].id}, 'text',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-left"></i></button><p style="text-align: center;width: 100%;">${data[i].filename}</p><button type="button" class="btn btn-primary" onclick="showPrevNextModal(${data[i].id},${data[i + 1].id}, 'text',${data[i].idfolder},'${root}')\"><i class="fas fa-arrow-alt-circle-right"></i></button>`;
        }
        html += "</div></div></div></div>";
      }

      html +=
        '<ul class="nav explore-scroll-top">' +
        '<li><a href="#topexplore" title="Top">' +
        '<i class="fas fa-chevron-circle-up fa-2x"></i>' +
        "</a>" +
        "</li></ul>";
      $("#tabloid").append(html);
    } else {
      console.log("AU SECOURS");
    }
  });
}

function exploreThesaurus(root, thesaurus, idThes) {
  let html = "";
  $("#tabloid").empty();
  $(".alert").remove();

  $.ajax({
    url: `/exploreThes,${root},${thesaurus},${idThes}`,
  }).done((data) => {
    if (typeof data[0] !== "undefined") {
      html +=
        `<div class="d-flex justify-content-between" id="topexplore"><span style="font-size: 1.6em">${thesaurus} > ${data[0].short_name}</span></div>`;
      html += '<br><div class="listTabloid">';
      for (let i = 0; i < data.data_length; i++) {
        // TODO : pour le moment, on enlève les doublons objets / file ou folder
        // TODO en supprimant les objets de la liste des item visibles
        if (data[i].item_type !== "object") {
          html += '<div class="card text-center">';
          if (root === "conservatoire3d") html += `<a href="/3drepository/${data[i].idfolder}" >`;
          else if ((root === "pft3d") || (root === "corpus")) html += `<a href="/projectv/${data[i].idfolder}" >`;
          else {
            // TODO : prévoir une url pour les objects ??
            html += `<a href="/${data[i].idfolder}" >`;
          }
          if (data[i].nakala) {
            html += `<img class="card-img mx-auto d-block" src="${data[i].nakala}" alt="lien Nakala"></a>`;
          } else {
            html +=
              `<img class="card-img mx-auto d-block" src="/media/image?fid=${data[i].idfolder}&id=${data[i].id}&format=${data[i].srcImgThumb}&type=thumb&root=${root}" alt="${data[i].filename}"></a>`;
          }

          html += '<div class="card-body">';
          if (data[i].folder_name)
            if (data[i].nb_objects) html += `<small>${data[i].depot_name} [${data[i].nb_objects}]</small> `;
            else html += `<small>${data[i].depot_name}</small> `;
          else html += "<small></small> ";
          html += '<p class="card-text">' + '<p class="d-flex justify-content-between">' + "</p></p></div></div>";
        }
      }
      html += "</div>";
      html +=
        '<ul class="nav explore-scroll-top">' +
        '<li><a href="#topexplore" title="Top">' +
        '<i class="fas fa-chevron-circle-up fa-2x"></i>' +
        "</a>" +
        "</li></ul>";
      $("#tabloid").append(html);
    } else {
      console.log("AU SECOURS");
    }
  });
}

function exploreThesaurusMultiPage(root, projectId, thesaurus, idThes, userId, nb_item, path) {
  let html = "";
  $("#explore-div").show();
  // on ne donne pas la possibilité d'indexer quoi que ce soit depuis ici mais ça peut évoluer
  $("#item-selection").hide();
  $("#item-index-all").hide();

  $("#explore-results").empty();
  $(".alert").remove();

  html +=
    '<div id="topexplore"></div>' +
    '<br><div class="d-flex justify-content-between"><span id="nbResults"></span>' +
    //'<span><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span>' +
    "</div>" +
    '<div class="results"></div><br>' +
    //'<div class="d-flex justify-content-center"><span style="margin: auto;"><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<ul class="nav explore-scroll-top"><li><a href="#topexplore" title="Top"><i class="fas fa-chevron-circle-up fa-2x"></i></a></li></ul>';
  $("#explore-results").append(html);
  const WRights = 0;

  // Premiere partie: on renvoie le nombre de résultat seulement /exploreThesMultiNB
  if (parseInt(nb_item)) {
    $.ajax({
      url: `/exploreThesMultiNB,${root},${thesaurus},${idThes}`,
      data: { thes_path: path, projectId: projectId, nb_item: nb_item },
    }).done((data) => {
      //if (data.thesname)
      $("#topexplore").html(`<span style="font-size: 1.6em">${data.thesname}</span>`);
      if (data.nbAccess !== 0) {
        if (data.nbAccess > 1) $("#nbResults").html(`${data.nbAccess} documents `);
        else $("#nbResults").html(`${data.nbAccess} document `);
      }
      totalPage = data.pagination;
      // on récupère la première page de résultats
      loadThesMulti(root, projectId, thesaurus, idThes, path, userId, 1);
    });
  }
}

function exploreThesaurusPactolsPage(root, projectId, thesaurus, idThes, userId, nb_item, path) {
  // Use the unified exploration function with pactols type
  exploreThesaurusItems(thesaurus, idThes, 'pactols');
  return;
}

// pour consulter les items attachés à un concept, quelque soit son thesaurus de départ
function exploreThesaurusConceptMultiPage(root, projectId, idThes, userId) {
  let html = "";
  $("#tabloid").empty();
  $(".alert").remove();

  html +=
    '<div id="topexplore"></div>' +
    '<br><div class="d-flex justify-content-between"><span id="nbResults"></span><span><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<div class="results"></div><br><div class="d-flex justify-content-center"><span style="margin: auto;"><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<ul class="nav explore-scroll-top"><li><a href="#topexplore" title="Top"><i class="fas fa-chevron-circle-up fa-2x"></i></a></li></ul>';
  $("#tabloid").append(html);
  const WRights = 0;

  // Premiere partie: on renvoie le nombre de résultat seulement /exploreThesMultiNBGeneral

  $.ajax({
    url: `/exploreThesMultiNBGeneral,${root},${idThes}`,
    data: { projectId: projectId },
  }).done((data) => {
    //if (data.thesname)
    $("#topexplore").html(`<span style="font-size: 1.6em">${data.thesname}</span>`);
    if (data.nbAccess !== 0) {
      if (data.nbAccess > 1) $("#nbResults").html(`${data.nbAccess} documents `);
      else $("#nbResults").html(`${data.nbAccess} document `);
    }
    /*
                $('#topexplore').html('<span style="font-size: 1.6em">' + data.thesname + '</span>')
            //if (data.nbAccess !== 0)
                //$('#nbResults').html(data.nbAccess + ' documents accessibles<br>' + nb_item + ' documents en tout');
             if (data.nbAccess !== 0)   {
                 if (data.nbAccess > 1) $('#nbResults').html(data.nbAccess + ' documents ');
                 else $('#nbResults').html(data.nbAccess + ' document ');
             }

             */
    totalPage = data.pagination;
    // on récupère la première page de résultats
    loadThesMulti(root, projectId, thesaurus, idThes, userId, 1);
  });
}

function exploreThesConceptMultiPrepare(branch, lng, projectId, userId, genre, thesaurus, id_dom) {
  const dataPost = {};
  let dom = genre;
  let text_result = "";
  $("#explore-div").show();
  // on ne donne pas la possibilité d'indexer quoi que ce soit depuis ici mais ça peut évoluer
  $("#item-selection").hide();
  $("#item-index-all").hide();

  $("#explore-results").empty();

  $(".alert").remove();
  let html = "";
  if (lng === "fr") text_result = no_results_fr;
  else text_result = no_results_en;

  html +=
    '<div id="topexplore"></div>' +
    '<br><div class="d-flex justify-content-between"><span id="nbResults"></span>' +
    //'<span><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span>' +
    "</div>" +
    '<div class="results"></div><br>' +
    //'<div class="d-flex justify-content-center"><span style="margin: auto;"><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<ul class="nav explore-scroll-top"><li><a href="#topexplore" title="Top"><i class="fas fa-chevron-circle-up fa-2x"></i></a></li></ul>';
  $("#explore-results").append(html);

  console.log(id_dom);
  if (genre === "pactols") dom = "";
  dataPost.value = $(`#opentheso${dom}_${thesaurus}_value`).val();
  dataPost.id = $(`#opentheso${dom}_${thesaurus}_id`).val().split('##')[0];
  dataPost.thesaurus = $(`#opentheso${dom}_${thesaurus}_id`).val().split('##')[1];

  console.log(dataPost);
  // on récupère l'id_thes de l'autocomplete (view thesaurus.ejs)
  // le thesaurus est general
  $.ajax({
    url: `/exploreThesMultiNBgeneral,${branch},${dataPost.id}`,
    data: { projectId: projectId, thesaurus: dataPost.thesaurus },
  }).done((data) => {
    $("#topexplore").html(`<span style="font-size: 1.6em">${data.thesname}</span>`);
    if (data.nbAccess !== 0) {
      if (data.nbAccess > 1) $("#nbResults").html(`${data.nbAccess} documents `);
      else $("#nbResults").html(`${data.nbAccess} document `);
    } else $("#nbResults").html(text_result);
    $("#openthesomulti_general_value").val("");
    totalPage = data.pagination;
    // on récupère la première page de résultats
    // maintenant seulement sur le thesaurus ciblé !!!
    loadThesMulti(branch, projectId, dataPost.thesaurus, dataPost.id, "_", userId, 1);
  });
}

// pour thesaurus simple
function exploreThesConceptPrepare(branch, lng, projectId, userId, genre, thesaurus) {
  // thesaurus = general ?
  const dataPost = {};
  const dom = genre; // "thesaurus"
  let text_result = "";
  $("#tabloid").empty();
  $(".alert").remove();
  let html = "";
  if (lng === "fr") text_result = no_results_fr;
  else text_result = no_results_en;

  html +=
    '<div id="topexplore"></div>' +
    '<br><div class="d-flex justify-content-between"><span id="nbResults"></span><span><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<div class="results"></div><br><div class="d-flex justify-content-center"><span style="margin: auto;"><nav aria-label="Page navigation"><ul class="pagination"></ul></nav></span></div>' +
    '<ul class="nav explore-scroll-top"><li><a href="#topexplore" title="Top"><i class="fas fa-chevron-circle-up fa-2x"></i></a></li></ul>';
  $("#tabloid").append(html);


  dataPost.value = $(`#opentheso${dom}_${thesaurus}_value`).val();
  dataPost.id = $(`#opentheso${dom}_${thesaurus}_id`).val().split('##')[0]
  dataPost.thesaurus = $(`#opentheso${dom}_${thesaurus}_id`).val().split('##')[1]

  console.log(dataPost);
  // on récupère l'id_thes de l'autocomplete (view thesaurus.ejs)
  // le thesaurus est general
  $.ajax({
    url: `/exploreThesNBGeneral,${branch},${dataPost.id}`,
    data: { projectId: projectId, thesaurus: dataPost.thesaurus },
  }).done((data) => {
    //if (data.thesname)
    $("#topexplore").html(`<span style="font-size: 1.6em">${data.thesname}</span>`);
    //if (data.nbAccess !== 0)
    //$('#nbResults').html(data.nbAccess + ' documents accessibles<br>' + nb_item + ' documents en tout');
    if (data.nbAccess !== 0) {
      if (data.nbAccess > 1) $("#nbResults").html(`${data.nbAccess} documents `);
      else $("#nbResults").html(`${data.nbAccess} document `);
    } else $("#nbResults").html(text_result);
    $("#openthesothesaurus_general_value").val("");
    totalPage = data.pagination;
    // on récupère la première page de résultats
    // maintenant seulement sur le thesaurus ciblé !!!
    loadThesSimple(branch, projectId, dataPost.thesaurus, dataPost.id, "_", userId, 1);
  });
}

// appeler la requête sur la bonne portion de pages
function loadThesMulti(root, projectId, thesaurus, idThes, path, userId, page) {
  const resultdiv = $(".results");
  $.ajax({
    url: `/exploreThesMultiPage,${root},${thesaurus},${idThes},${page}`,
    data: { thes_path: path, projectId: projectId },
  }).done((data) => {
    resultdiv.html(data); // on affiche le résultat dans la div
    paginationthes(root, projectId, thesaurus, idThes, path, userId, page); // on active la pagination
  });
}

function paginationthes(root, projectId, thesaurus, idThes, path, userId, encours) {
  const total = totalPage;
  //console.log('pagination page en cours '+ encours + ' sur '+ total);
  //document.cookie = "pageEncours="+encours;
  const pagination = $(".pagination"); // ul class="pagination" dans tabloid
  pagination.html("");
  if (encours > 1) {
    pagination.append(
      `<li class="page-item"><a class="page-link" onclick="loadThesMulti(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',,${userId},${encours - 1});" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>`
    );
  }
  for (let i = 1; i <= total; i++) {
    if (i === encours) {
      pagination.append(`<li class="page-item active"><a class="page-link" href="#">${i}</a></li>`);
    } else if (i > encours - 5 && i < encours + 5) {
      pagination.append(
        `<li class="page-item"><a class="page-link"  onclick="loadThesMulti(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',${userId},${i});" href="#">${i}</a></li>`
      );
    }
  }
  if (encours < total) {
    pagination.append(
      `<li class="page-item"><a class="page-link" onclick="loadThesMulti(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',${userId},${encours + 1});" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>`
    );
  }
}

function getThesaurusTree(root, projectId, name, thesId, userId) {
  let htmltree = "";
  // on commence par vider le tabloid avant d'affichier qqch avec un exploreThesaurus ?
  $("#tabloid").empty();
  $("#tabloid").removeClass("container");
  $("#tabloid").addClass("container-fluid");
  //$('#tabloid').html('<img src="/assets/images/image_'+root+'.jpg" style="max-height:99%;max-width:99%;margin-left:auto;margin-right:auto;display:block;opacity: 0.5;">')
  //$('#tabloid').html('<img src="/assets/images/image_'+root+'.jpg" style="max-height:99%;max-width:99%;margin-right:auto;display:block;opacity: 0.2;">')

  $(".MainPageThesaurus").removeClass("active");
  $(".MainPageThesaurusSimple").removeClass("active");
  $(`#${name}`).addClass("active");

  $(".alert").remove();

  $.ajax({
    url: `/getThesaurusTreeData/${root},${name},${thesId}`,
  }).done((data) => {
    htmltree += '<br><br><ul id="main-ul" style="padding-left:0em!important;">';

    const indice = [];
    const profond = [];
    let ind = 0;
    for (let i = 0; i < data.length; i++) {
      // on ramène tout même ceux qui n'ont pas d'item pour pouvoir récupérer leurs enfants qui ont des items (nb_tot_item != 0)
      if (data[i].depth > 1) {
        // il ne faut pas afficher le thesaurus item racine (ca ne sert à rien)
        if (data[i].get_children === null) {
          if (profond[ind - 1] > data[i].depth) {
            // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
            if (i - indice[ind - 1] > 1) {
              let profondeur2 = profond[ind - 1] - data[i].depth;
              if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
              for (let p = 0; p < profondeur2; p++) {
                htmltree += "</ul></li>";
              }
            }
          }
          // pas d'enfant , regarder le suivant
          for (let j = i; j < data.length; j++) {
            if (j === i + 1) {
              //si c'est la même profondeur : affichage simple
              if (data[i].depth === data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].short_name} [${data[i].nb_item}]</a>`;
                else htmltree += data[i].short_name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li>";
              }
              // sinon, on change de profondeur, il faut refermer la liste
              else if (data[i].depth > data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item) {
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].short_name} [${data[i].nb_item}]</a>`;
                } else htmltree += data[i].short_name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li> ";
                // si on remonte de plus de 1 niveau , il faut fermer les niveaux intermédiaires
                if (data[i].depth - data[j].depth > 1) {
                  //il faut fermer autant de fois qu'il y a de différence entre les 2 niveaux
                  for (let k = data[j].depth; k < data[i].depth; k++) {
                    htmltree += "</ul></li>";
                  }
                  // enfin on referme le dernier niveau
                } else {
                  htmltree += "</ul></li>";
                }
              }
            }
          }
          // si c'est le dernier (pas de suivant !)
          if (i === data.length - 1) {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
            if (data[i].nb_item) {
              htmltree +=
                `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].short_name} [${data[i].nb_item}]</a>`;
            } else htmltree += data[i].short_name;
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "</li>";
            // TODO test si c'est le seul (data[i-1] undefined!!!)
            if (typeof data[i - 1] !== "undefined") {
              if (data[i].depth !== data[i - 1].depth) {
                //On regarde la profondeur de celui d'avant
                const profondeur = data[i].depth;
                for (let pr = 0; pr < profondeur; pr++) {
                  console.log(pr);
                  htmltree += "</ul></li>";
                }
              }
            }
          }
        }
        // enfants : on met en place l'arborescence
        else {
          if (i === 0) {
            htmltree +=
              `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
            if (data[i].nb_tot_item) {
              if (data[i].nb_item)
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].short_name} [${data[i].nb_item}]`;
              else
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].short_name}`;
              //if (data[i]['nb_item'] != 0) htmltree += ' [' + data[i]['nb_item'] + ']'
              htmltree += ` (${data[i].nb_tot_item})</label></a>`;
            }
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "<ul>";
          } else {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            for (let j = i; j < data.length; j++) {
              // en regardant le suivant
              if (j === i + 1) {
                // le suivant sera visible on mets en place l'arborescence
                // Mais erreurs dans le cas ou le précédent visible était d'une depth plus petite :
                // on n'a pas refermée le ul .......
                htmltree +=
                  `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].short_name} [${data[i].nb_item}]`;
                else
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].short_name}`;
                //if (data[i]['nb_item'] != 0) htmltree += ' [' + data[i]['nb_item'] + ']'
                if (data[i].nb_tot_item) htmltree += ` (${data[i].nb_tot_item})`;
                htmltree += "</label></a>";
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "<ul>";
              }
            }
          }
        }
      }
    }
    htmltree += "</ul>";
    //htmltree += '</div>'
    $("#menuGauche").html(htmltree);
  });
}

function loadThesSimple(root, projectId, thesaurus, idThes, path, userId, page) {
  const resultdiv = $(".results");
  $.ajax({
    url: `/exploreThesPage,${root},${thesaurus},${idThes},${page}`,
    data: { thes_path: path, projectId: projectId },
  }).done((data) => {
    resultdiv.html(data); // on affiche le résultat dans la div
    paginationthesSimple(root, projectId, thesaurus, idThes, path, userId, page); // on active la pagination
  });
}

function paginationthesSimple(root, projectId, thesaurus, idThes, path, userId, encours) {
  const total = totalPage;

  const pagination = $(".pagination");
  pagination.html("");
  if (encours > 1) {
    pagination.append(
      `<li class="page-item"><a class="page-link" onclick="loadThesSimple(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',,${userId},${encours - 1});" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>`
    );
  }
  for (let i = 1; i <= total; i++) {
    if (i === encours) {
      pagination.append(`<li class="page-item active"><a class="page-link" href="#">${i}</a></li>`);
    } else if (i > encours - 5 && i < encours + 5) {
      pagination.append(
        `<li class="page-item"><a class="page-link"  onclick="loadThesSimple(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',${userId},${i});" href="#">${i}</a></li>`
      );
    }
  }
  if (encours < total) {
    pagination.append(
      `<li class="page-item"><a class="page-link" onclick="loadThesSimple(\'${root}',${projectId},'${thesaurus}',${idThes},'${path}',${userId},${encours + 1});" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>`
    );
  }
}

function exploreThesaurusPage(root, projectId, thesaurus, idThes, userId, nb_item, path) {

  let html = "";
  $("#explore-div").show();
  // on ne donne pas la possibilité d'indexer quoi que ce soit depuis ici mais ça peut évoluer
  $("#item-selection").hide();
  $("#item-index-all").hide();

  $("#explore-results").empty();
  $(".alert").remove();

  html +=
    '<div id="topexplore"></div>' +
    '<br><div class="d-flex justify-content-between"><span id="nbResults"></span>' +
    "</div>" +
    '<div class="results"></div><br>' +
    '<ul class="nav explore-scroll-top"><li><a href="#topexplore" title="Top"><i class="fas fa-chevron-circle-up fa-2x"></i></a></li></ul>';
  $("#explore-results").append(html);

  // Premiere partie: on renvoie le nombre de résultat seulement /exploreThesNB
  if (parseInt(nb_item)) {
    $.ajax({
      url: `/exploreThesNB,${root},${thesaurus},${idThes}`,
      data: { thes_path: path, projectId: projectId },
    }).done((data) => {
      //if (data.thesname)
      $("#topexplore").html(`<span style="font-size: 1.6em">${data.thesname}</span>`);
      if (data.nbAccess !== 0) {
        if (data.nbAccess > 1) $("#nbResults").html(`${data.nbAccess} documents `);
        else $("#nbResults").html(`${data.nbAccess} document `);
      }
      totalPage = data.pagination;
      // on récupère la première page de résultats
      loadThesSimple(root, projectId, thesaurus, idThes, path, userId, 1);
    });
  }
}

function getThesaurusTreePactols(root, projectId, name, thesId, thesPath, userId) {
  let htmltree = "";
  // on commence par vider le tabloid avant d'affichier qqch avec un exploreThesaurus ?
  $("#tabloid").empty();
  $("#tabloid").removeClass("container");
  $("#tabloid").addClass("container-fluid");

  $(".MainPageThesaurus").removeClass("active");
  $(".MainPageThesaurusSimple").removeClass("active");
  $(`#${name}`).addClass("active");

  $(".alert").remove();

  $.ajax({
    url: `/getThesaurusTreeDataPactols/${root},${name},${thesPath}`,
  }).done((data) => {
    htmltree += '<br><br><ul id="main-ul" style="padding-left:0em!important;">';

    const indice = [];
    const profond = [];
    let ind = 0;
    for (let i = 0; i < data.length; i++) {
      // on ramène tout même ceux qui n'ont pas d'item pour pouvoir récupérer leurs enfants qui ont des items (nb_tot_item != 0)
      if (data[i].depth > 1) {
        // il ne faut pas afficher le thesaurus item racine (ca ne sert à rien)
        if (data[i].get_children === null) {
          if (profond[ind - 1] > data[i].depth) {
            // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
            if (i - indice[ind - 1] > 1) {
              let profondeur2 = profond[ind - 1] - data[i].depth;
              if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
              for (let p = 0; p < profondeur2; p++) {
                htmltree += "</ul></li>";
              }
            }
          }
          // pas d'enfant , regarder le suivant
          for (let j = i; j < data.length; j++) {
            if (j === i + 1) {
              //si c'est la même profondeur : affichage simple
              if (data[i].depth === data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
                else htmltree += data[i].name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li>";
              }
              // sinon, on change de profondeur, il faut refermer la liste
              else if (data[i].depth > data[j].depth) {
                htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
                if (data[i].nb_item) {
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
                } else htmltree += data[i].name;
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "</li> ";
                // si on remonte de plus de 1 niveau , il faut fermer les niveaux intermédiaires
                if (data[i].depth - data[j].depth > 1) {
                  //il faut fermer autant de fois qu'il y a de différence entre les 2 niveaux
                  for (let k = data[j].depth; k < data[i].depth; k++) {
                    htmltree += "</ul></li>";
                  }
                  // enfin on referme le dernier niveau
                } else {
                  htmltree += "</ul></li>";
                }
              }
            }
          }
          // si c'est le dernier (pas de suivant !)
          if (i === data.length - 1) {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            htmltree += '<li><i class="far fa-folder"></i>&nbsp;';
            if (data[i].nb_item) {
              htmltree +=
                `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" >${data[i].name} [${data[i].nb_item}]</a>`;
            } else htmltree += data[i].name;
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "</li>";
            // TODO test si c'est le seul (data[i-1] undefined!!!)
            if (typeof data[i - 1] !== "undefined") {
              if (data[i].depth !== data[i - 1].depth) {
                //On regarde la profondeur de celui d'avant
                const profondeur = data[i].depth;
                for (let pr = 0; pr < profondeur; pr++) {
                  console.log(pr);
                  htmltree += "</ul></li>";
                }
              }
            }
          }
        }
        // enfants : on met en place l'arborescence
        else {
          if (i === 0) {
            htmltree +=
              `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
            if (data[i].nb_tot_item) {
              if (data[i].nb_item)
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name} [${data[i].nb_item}]`;
              else
                htmltree +=
                  `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name}`;
              htmltree += ` (${data[i].nb_tot_item})</label></a>`;
            }
            profond[ind] = data[i].depth;
            indice[ind] = i;
            ind++;
            htmltree += "<ul>";
          } else {
            if (profond[ind - 1] > data[i].depth) {
              // dans le cas ou le dernier élément affiché précédemment n'est pas celui juste avant
              if (i - indice[ind - 1] > 1) {
                let profondeur2 = profond[ind - 1] - data[i].depth;
                if (profondeur2 > 2) profondeur2 = profondeur2 - 1;
                for (let p = 0; p < profondeur2; p++) {
                  htmltree += "</ul></li>";
                }
              }
            }
            for (let j = i; j < data.length; j++) {
              // en regardant le suivant
              if (j === i + 1) {
                // le suivant sera visible on mets en place l'arborescence
                // Mais erreurs dans le cas ou le précédent visible était d'une depth plus petite :
                // on n'a pas refermée le ul .......
                htmltree +=
                  `<li><input type="checkbox" id="${data[i].id}" /><i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;`;
                if (data[i].nb_item)
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},${data[i].nb_item},'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name} [${data[i].nb_item}]`;
                else
                  htmltree +=
                    `<a href="#" onclick="exploreThesaurusPactolsPage(\'${root}',${projectId},'${name}',${data[i].id}, ${userId},0,'${data[i].path}\')" style="text-decoration: none;font-weight: bolder;" ><label class="fakelink" for="${data[i].id}" style="margin-bottom: 0px;">${data[i].name}`;
                if (data[i].nb_tot_item) htmltree += ` (${data[i].nb_tot_item})`;
                htmltree += "</label></a>";
                profond[ind] = data[i].depth;
                indice[ind] = i;
                ind++;
                htmltree += "<ul>";
              }
            }
          }
        }
      }
    }
    htmltree += "</ul>";
    $("#menuGauche").html(htmltree);
  });
}
